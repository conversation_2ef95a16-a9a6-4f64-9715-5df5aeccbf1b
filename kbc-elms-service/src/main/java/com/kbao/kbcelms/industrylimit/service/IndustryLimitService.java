package com.kbao.kbcelms.industrylimit.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.kbao.commons.web.PageRequest;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcbsc.util.BscUserUtils;
import com.kbao.kbcelms.formConfig.model.FormConfigField;
import com.kbao.kbcelms.industrylimit.bean.IndustryLimitFieldOptionVO;
import com.kbao.kbcelms.industrylimit.bean.IndustryLimitQuery;
import com.kbao.kbcelms.industrylimit.dao.IndustryLimitActionMapper;
import com.kbao.kbcelms.industrylimit.dao.IndustryLimitConditionMapper;
import com.kbao.kbcelms.industrylimit.dao.IndustryLimitMapper;
import com.kbao.kbcelms.industrylimit.entity.IndustryLimit;
import com.kbao.kbcelms.industrylimit.entity.IndustryLimitAction;
import com.kbao.kbcelms.industrylimit.entity.IndustryLimitCondition;
import com.kbao.kbcelms.industrylimit.vo.IndustryLimitActionVO;
import com.kbao.kbcelms.industrylimit.vo.IndustryLimitConditionVO;
import com.kbao.kbcelms.industrylimit.vo.IndustryLimitVO;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.kbao.tool.util.SysLoginUtils;
import com.kbao.kbcelms.formConfig.service.FormConfigService;
import com.kbao.kbcelms.common.constants.ElmsConstant;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 行业限制规则服务实现类
 * <AUTHOR>
 * @date 2025-08-08
 */
@Service
public class IndustryLimitService extends BaseSQLServiceImpl<IndustryLimit, Long, IndustryLimitMapper> {

    @Autowired
    private IndustryLimitConditionMapper conditionMapper;

    @Autowired
    private IndustryLimitActionMapper actionMapper;
    
    @Autowired
    private FormConfigService formConfigService;

    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 分页查询行业限制规则
     * @param request 分页请求
     * @return 分页结果
     */
    public PageInfo<IndustryLimitVO> getPage(PageRequest<IndustryLimitQuery> request) {
        PageHelper.startPage(request.getPageNum(), request.getPageSize());
        List<IndustryLimitVO> list = mapper.selectIndustryLimitList(request.getParam());

        // 为每个VO设置状态名称并加载关联数据
        for (IndustryLimitVO vo : list) {
            vo.setStatusName(vo.getStatus() == 1 ? "启用" : "禁用");
            
            // 查询条件列表
            List<IndustryLimitCondition> conditions = conditionMapper.selectByRuleId(vo.getId());
            vo.setConditions(convertConditionsToVO(conditions));

            // 查询执行动作列表
            List<IndustryLimitAction> actions = actionMapper.selectByRuleId(vo.getId());
            vo.setActionList(convertActionsToVO(actions));
        }

        return new PageInfo<>(list);
    }

    /**
     * 根据ID查询行业限制规则详情
     * @param id 规则ID
     * @return 规则详情
     */
    public IndustryLimitVO getById(Long id) {
        IndustryLimitVO vo = mapper.selectIndustryLimitById(id);
        if (vo != null) {
            // 查询条件列表
            List<IndustryLimitCondition> conditions = conditionMapper.selectByRuleId(id);
            vo.setConditions(convertConditionsToVO(conditions));

            // 查询执行动作列表
            List<IndustryLimitAction> actions = actionMapper.selectByRuleId(id);
            vo.setActionList(convertActionsToVO(actions));

            // 设置状态名称
            vo.setStatusName(vo.getStatus() == 1 ? "启用" : "禁用");
        }
        return vo;
    }

    /**
     * 新增行业限制规则
     * @param vo 规则信息
     * @return 新增结果
     */
    @Transactional(rollbackFor = Exception.class)
    public IndustryLimitVO add(IndustryLimitVO vo) {
        // 检查编码是否重复
        if (checkCodeExists(vo.getCode(), null)) {
            throw new RuntimeException("规则编码已存在");
        }

        // 保存主表
        IndustryLimit entity = new IndustryLimit();
        BeanUtils.copyProperties(vo, entity);
        entity.setCreateTime(LocalDateTime.now());
        entity.setUpdateTime(LocalDateTime.now());
        entity.setCreateUser(BscUserUtils.getUserId());
        entity.setUpdateUser(BscUserUtils.getUserId());
        entity.setTenantId(SysLoginUtils.getUser().getTenantId());
        entity.setIsDeleted(0);

        insertSelective(entity);

        // 保存条件
        saveConditions(entity.getId(), vo.getConditions());

        // 保存执行动作
        saveActions(entity.getId(), vo.getActionList());

        return getById(entity.getId());
    }

    /**
     * 更新行业限制规则
     * @param vo 规则信息
     * @return 更新结果
     */
    @Transactional(rollbackFor = Exception.class)
    public IndustryLimitVO update(IndustryLimitVO vo) {
        // 检查编码是否重复
        if (checkCodeExists(vo.getCode(), vo.getId())) {
            throw new RuntimeException("规则编码已存在");
        }

        // 更新主表
        IndustryLimit entity = new IndustryLimit();
        BeanUtils.copyProperties(vo, entity);
        entity.setUpdateTime(LocalDateTime.now());
        entity.setUpdateUser(BscUserUtils.getUserId());

        updateByPrimaryKeySelective(entity);

        // 删除原有条件和动作
        conditionMapper.deleteByRuleId(vo.getId());
        actionMapper.deleteByRuleId(vo.getId());

        // 保存新的条件和动作
        saveConditions(vo.getId(), vo.getConditions());
        saveActions(vo.getId(), vo.getActionList());

        return getById(vo.getId());
    }

    /**
     * 删除行业限制规则
     * @param id 规则ID
     */
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        // 逻辑删除主表
        IndustryLimit entity = new IndustryLimit();
        entity.setId(id);
        entity.setIsDeleted(1);
        entity.setUpdateTime(LocalDateTime.now());
        entity.setUpdateUser(BscUserUtils.getUserId());
        updateByPrimaryKeySelective(entity);

        // 删除条件和动作
        int result = conditionMapper.deleteByRuleId(id);
        if(result >= 0) { // 修改条件，允许删除0条记录（当没有条件时）
            return actionMapper.deleteByRuleId(id);
        }
        return result;
    }

    /**
     * 检查规则编码是否存在
     * @param code 规则编码
     * @param id 排除的ID
     * @return 是否存在
     */
    public boolean checkCodeExists(String code, Long id) {
        return mapper.checkCodeExists(code, id) > 0;
    }

    /**
     * 转换为VO对象
     */
    private IndustryLimitVO convertToVO(IndustryLimit entity) {
        IndustryLimitVO vo = new IndustryLimitVO();
        BeanUtils.copyProperties(entity, vo);
        vo.setStatusName(entity.getStatus() == 1 ? "启用" : "禁用");
        return vo;
    }

    /**
     * 保存条件列表
     */
    private void saveConditions(Long ruleId, List<IndustryLimitConditionVO> conditions) {
        if (!CollectionUtils.isEmpty(conditions)) {
            for (int i = 0; i < conditions.size(); i++) {
                IndustryLimitConditionVO conditionVO = conditions.get(i);
                IndustryLimitCondition condition = new IndustryLimitCondition();
                BeanUtils.copyProperties(conditionVO, condition);
                condition.setRuleId(ruleId);
                condition.setSortOrder(i + 1);
                condition.setCreateTime(LocalDateTime.now());
                condition.setTenantId(SysLoginUtils.getUser().getTenantId());
                conditionMapper.insert(condition);
            }
        }
    }

    /**
     * 保存执行动作列表
     */
    private void saveActions(Long ruleId, List<IndustryLimitActionVO> actions) {
        if (!CollectionUtils.isEmpty(actions)) {
            for (int i = 0; i < actions.size(); i++) {
                IndustryLimitActionVO actionVO = actions.get(i);
                IndustryLimitAction action = new IndustryLimitAction();
                BeanUtils.copyProperties(actionVO, action);
                action.setRuleId(ruleId);
                action.setSortOrder(i + 1);
                action.setCreateTime(LocalDateTime.now());
                action.setTenantId(SysLoginUtils.getUser().getTenantId());

                // 转换服务ID列表为JSON字符串
                if (!CollectionUtils.isEmpty(actionVO.getServiceIds())) {
                    try {
                        action.setServiceIds(objectMapper.writeValueAsString(actionVO.getServiceIds()));
                    } catch (Exception e) {
                        throw new RuntimeException("服务ID列表转换失败", e);
                    }
                }

                actionMapper.insert(action);
            }
        }
    }

    /**
     * 转换条件列表为VO
     */
    private List<IndustryLimitConditionVO> convertConditionsToVO(List<IndustryLimitCondition> conditions) {
        List<IndustryLimitConditionVO> voList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(conditions)) {
            for (IndustryLimitCondition condition : conditions) {
                IndustryLimitConditionVO vo = new IndustryLimitConditionVO();
                BeanUtils.copyProperties(condition, vo);
                voList.add(vo);
            }
        }
        return voList;
    }

    /**
     * 转换执行动作列表为VO
     */
    private List<IndustryLimitActionVO> convertActionsToVO(List<IndustryLimitAction> actions) {
        List<IndustryLimitActionVO> voList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(actions)) {
            for (IndustryLimitAction action : actions) {
                IndustryLimitActionVO vo = new IndustryLimitActionVO();
                BeanUtils.copyProperties(action, vo);

                // 转换JSON字符串为服务ID列表
                if (StringUtils.hasText(action.getServiceIds())) {
                    try {
                        List<String> serviceIds = objectMapper.readValue(action.getServiceIds(), 
                            new TypeReference<List<String>>() {});
                        vo.setServiceIds(serviceIds);
                    } catch (Exception e) {
                        vo.setServiceIds(new ArrayList<>());
                    }
                }

                voList.add(vo);
            }
        }
        return voList;
    }

    /**
     * 获取字段选项列表
     * @return 字段选项列表
     */
    public List<IndustryLimitFieldOptionVO> getFieldOptions() {
        List<IndustryLimitFieldOptionVO> options = new ArrayList<>();
        
        try {
            // 调用FormConfigService获取代理企业补充表单的字段列表
            List<FormConfigField> fieldList =
                formConfigService.getFieldList(ElmsConstant.FORM_CONFIG_AGENT_ENTERPRISE);
            
            if (fieldList != null && !fieldList.isEmpty()) {
                // 将FormConfigField转换为IndustryLimitFieldOptionVO
                for (FormConfigField field : fieldList) {
                    options.add(new IndustryLimitFieldOptionVO(
                        field.getFieldCode(), 
                        field.getFieldName(),
                        field.getShowType(),
                        field.getAdditional()
                    ));
                }
            } else {
                // 如果没有获取到字段，抛出异常
                throw new RuntimeException("没有获取到字段");
            }
        } catch (Exception e) {
            // 如果调用失败，抛出异常
            throw new RuntimeException("获取表单配置字段失败", e);
        }
        
        return options;
    }

    /**
     * 根据规则ID获取条件列表
     * @param ruleId 规则ID
     * @return 条件列表
     */
    public List<IndustryLimitCondition> getConditionsByRuleId(Long ruleId) {
        return conditionMapper.selectByRuleId(ruleId);
    }

    /**
     * 根据规则ID获取执行动作列表
     * @param ruleId 规则ID
     * @return 执行动作列表
     */
    public List<IndustryLimitAction> getActionsByRuleId(Long ruleId) {
        return actionMapper.selectByRuleId(ruleId);
    }

}
