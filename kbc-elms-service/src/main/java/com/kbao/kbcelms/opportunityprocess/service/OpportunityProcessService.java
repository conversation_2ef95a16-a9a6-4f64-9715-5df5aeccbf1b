package com.kbao.kbcelms.opportunityprocess.service;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.exception.BusinessException;
import com.kbao.commons.web.Result;
import com.kbao.feign.config.FeignRequestHeader;
import com.kbao.kbcbpm.process.vo.*;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcelms.bpm.ElmsBpmWebService;
import com.kbao.kbcelms.common.config.ElmsContext;
import com.kbao.kbcelms.enums.DoListEnum;
import com.kbao.kbcelms.enums.OpportunityStatusEnum;
import com.kbao.kbcelms.enums.ProcessTypeEnum;
import com.kbao.kbcelms.opportunity.entity.Opportunity;
import com.kbao.kbcelms.opportunity.service.OpportunityService;
import com.kbao.kbcelms.opportunitydetail.entity.OpportunityDetail;
import com.kbao.kbcelms.opportunitydetail.service.OpportunityDetailService;
import com.kbao.kbcelms.opportunityprocess.dao.OpportunityProcessMapper;
import com.kbao.kbcelms.opportunityprocess.entity.OpportunityProcess;
import com.kbao.kbcelms.opportunityprocesslog.entity.OpportunityProcessLog;
import com.kbao.kbcelms.opportunityprocesslog.service.OpportunityProcessLogService;
import com.kbao.kbcelms.opportunityteamdivision.model.OpportunityTeamDivisionVO;
import com.kbao.kbcelms.opportunityteamdivision.service.OpportunityTeamDivisionService;
import com.kbao.kbcelms.processdefine.entity.ProcessDefine;
import com.kbao.kbcelms.genAgentEnterprise.service.GenAgentEnterpriseService;
import com.kbao.kbcelms.genAgentEnterprise.entity.GenAgentEnterprise;
import com.kbao.kbcelms.processdefine.service.ProcessDefineService;
import com.kbao.kbcelms.user.entity.User;
import com.kbao.kbcelms.user.service.UserService;
import com.kbao.kbcelms.usertenant.entity.UserTenant;
import com.kbao.kbcelms.usertenant.service.UserTenantService;
import com.kbao.kbcucs.agent.model.AgentBaseVO;
import com.kbao.kbcucs.client.model.BaseUserSensitiveInfo;
import com.kbao.kbcucs.client.model.GetByAgentCodeReq;
import com.kbao.kbcucs.client.nonuser.AgentWebV2ClientService;
import com.kbao.tool.util.EmptyUtils;
import com.kbao.tool.util.SysLoginUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.kbao.kbcelms.constants.ElmsConstants.BUSINESS_KEY;

@Slf4j
@Service
public class OpportunityProcessService extends BaseSQLServiceImpl<OpportunityProcess, Integer,OpportunityProcessMapper> {

    @Autowired
    ElmsBpmWebService elmsBpmWebService;

    @Autowired
    OpportunityService opportunityService;

    @Autowired
    ProcessDefineService processDefineService;

    @Autowired
    AgentWebV2ClientService agentWebV2ClientService;

    @Autowired
    OpportunityProcessLogService opportunityProcessLogService;

    @Autowired
    UserService userService;

    @Autowired
    UserTenantService userTenantService;

    @Autowired
    OpportunityTeamDivisionService opportunityTeamDivisionService;

    @Autowired
    OpportunityDetailService  opportunityDetailService;
    
    @Autowired
    GenAgentEnterpriseService genAgentEnterpriseService;
    /**
     * 根据机会启动流程
     * @param opportunityId 机会id
     * @param companyType 公司类型  A  B  C  D
     */
    public void startProcess(Integer opportunityId,String companyType){
        Opportunity oppo = opportunityService.selectByPrimaryKey(opportunityId);
        if (EmptyUtils.isNotEmpty(oppo.getCurrentProcessId())){
            throw new BusinessException("当前流程已启动,不可重复启动流程 id："+oppo.getCurrentProcessId());
        }
        List<ProcessDefine> pds = processDefineService.getStartProcessDefineList();
        Map<String, String> headerMap = Maps.newHashMap();
        headerMap.put("tenantId", ElmsContext.getTenantId());
        FeignRequestHeader.Header.set(headerMap);
        GetByAgentCodeReq req = new GetByAgentCodeReq();
        req.setAgentCode(oppo.getAgentCode());
        BaseUserSensitiveInfo info = new BaseUserSensitiveInfo();
        info.setReturnOrg(true);
        req.setInfo(info);
        Result<AgentBaseVO> agent = agentWebV2ClientService.getAgentInfo(req);
        if (!ResultStatusEnum.isSuccess(agent.getResp_code())) {
            throw new BusinessException("提交机会代理人，数据无效");
        }
        //待启动的流程
        ProcessDefine currentProcess = null;
        // 查询能命中自定义的流程
        List<ProcessDefine> matchCustPds = pds.stream().filter(processDefine -> {
            if (processDefine.getProcessType().equals(ProcessTypeEnum.CUSTOM)) {
                
                //判断行业 - 通过agent_enterprise_id查询t_gen_agent_enterprise表的category_code字段
                if (EmptyUtils.isNotEmpty(processDefine.getCompanyIndustry())) {
                    String industryCode = getIndustryCodeByAgentEnterpriseId(oppo.getAgentEnterpriseId());
                    if (EmptyUtils.isEmpty(industryCode) || !Arrays.asList(processDefine.getCompanyIndustry().split(",")).contains(industryCode)) {
                        return false;
                    }
                }
                //判断企业规模
                if (EmptyUtils.isNotEmpty(processDefine.getCompanyType())) {
                    if (!Arrays.asList(processDefine.getCompanyType().split(",")).contains(companyType)) {
                        return false;
                    }
                }
                //判断代理人归属
                if (EmptyUtils.isNotEmpty(processDefine.getAgentLegalCode())) {
                    if (!Arrays.asList(processDefine.getAgentLegalCode().split(",")).contains(agent.getDatas().getLegalCode())) {
                        return false;
                    }
                    if (EmptyUtils.isNotEmpty(processDefine.getAgentTradingCenterCode())) {
                        if (!Arrays.asList(processDefine.getAgentTradingCenterCode().split(",")).contains(agent.getDatas().getLegalCode())) {
                            return false;
                        }
                    }
                }
                return true;
            }
            return false;
        }).collect(Collectors.toList());
        //若无自定义流程命中，取默认流程
        if (EmptyUtils.isEmpty(matchCustPds)){
            Optional<ProcessDefine> defaultPd = pds.stream().filter(processDefine -> processDefine.getProcessType().equals(ProcessTypeEnum.DEFAULT.getType())).findFirst();
            if (defaultPd.isPresent()){
                currentProcess = defaultPd.get();
            }
        }

        if (currentProcess==null){
            throw new BusinessException("未匹配到对应流程，请检查流程配置");
        }

        //启动流程
        ProcessStartVO param = new ProcessStartVO();
        param.setTenantId(ElmsContext.getTenantId());
        param.setProcessDefinitionKey(currentProcess.getProcessKey());
        param.setBusinessKey(BUSINESS_KEY);
        Result<ProcessInstanceVO> processInstance = elmsBpmWebService.startProcessInstanceWithBusinessKey(param);
        if (!ResultStatusEnum.isSuccess(processInstance.getResp_code())){
            throw new BusinessException(processInstance.getResp_msg());
        }
        //保存机会流程关联
        ProcessInstanceVO instanceVO = processInstance.getDatas();
        OpportunityProcess opportunityProcess = new OpportunityProcess();
        opportunityProcess.setTenantId(ElmsContext.getTenantId());
        opportunityProcess.setProcessKey(currentProcess.getProcessKey());
        opportunityProcess.setProcessName(currentProcess.getProcessName());
        opportunityProcess.setOpportunityId(opportunityId);
        opportunityProcess.setBpmProcessId(instanceVO.getId());
        opportunityProcess.setProcessStatus("1"); // 启动状态
        opportunityProcess.setCreateId(ElmsContext.getUser().getUserId());
        opportunityProcess.setCreateTime(new java.util.Date());
        opportunityProcess.setIsDeleted(0);
        this.insert(opportunityProcess);
        //更新流程Id
        oppo.setCurrentProcessId(opportunityProcess.getId());
        opportunityService.updateByPrimaryKeySelective(oppo);

        //查询流程节点一定义方式，指派任务领取人
        ProcessInstanceIdVO processInstanceIdVO = new ProcessInstanceIdVO();
        processInstanceIdVO.setProcessInstanceId(instanceVO.getId());
        Result<List<TaskVO>> task = elmsBpmWebService.getCurrentTaskInfoById(processInstanceIdVO);
        TaskVO taskNode = task.getDatas().get(0);
        ProcessDefine.NodeProperties properties = JSONObject.parseObject(JSONObject.toJSONString(taskNode.getCustomProperties()), ProcessDefine.NodeProperties.class);
        String assignee =null;
        List<String> candidateGroups = new ArrayList<>();
        if (properties.getAssignee()!=null){
            assignee = properties.getAssignee().getUserId();
        }else if (properties.getCandidateGroups()!=null){
            if (1 == properties.getCandidateGroups().getOrgType()){
                candidateGroups.add("org_type_all");
            }else{
                candidateGroups.add("org_type_"+agent.getDatas().getLegalCode());
            }
        }else{
            candidateGroups.add("org_type_"+agent.getDatas().getLegalCode());
        }
        TaskUserUpdateVO taskvO = new TaskUserUpdateVO();
        taskvO.setProcessInstanceId(instanceVO.getId());

        if(EmptyUtils.isNotEmpty(assignee)){
            taskvO.setAssignees(Arrays.asList(new String[]{assignee}));
        }
        if(EmptyUtils.isNotEmpty(candidateGroups)){
            taskvO.setCandidateGroups(candidateGroups);
        }
        log.info("taskvO:"+JSONObject.toJSONString(taskvO));
        Result<List<TaskVO>> res = elmsBpmWebService.setTaskDealUserById(taskvO);
        if (!ResultStatusEnum.isSuccess(res.getResp_code())){
            throw new BusinessException(res.getResp_msg());
        }
    }

    /**
     * 根据机会ID更新租户ID
     * @param opportunityId 机会ID
     * @param newTenantId 新的租户ID
     */
    public void updateTenantIdByOpportunityId(Integer opportunityId, String newTenantId) {
        if (opportunityId == null) {
            throw new BusinessException("机会ID不能为空");
        }
        if (EmptyUtils.isEmpty(newTenantId)) {
            throw new BusinessException("新租户ID不能为空");
        }
        
        // 查询该机会的所有流程关联记录
        Map<String, Object> param = new HashMap<>();
        param.put("opportunityId", opportunityId);
        List<OpportunityProcess> processes = this.selectByParam(param);
        
        // 批量更新租户ID
        for (OpportunityProcess process : processes) {
            process.setTenantId(newTenantId);
            process.setUpdateId(SysLoginUtils.getUserId());
            process.setUpdateTime(new Date());
            this.updateByPrimaryKey(process);
        }
    }

    /**
     * 根据机会ID判断当前机会状态是否可执行任务
     * @param opportunityId 机会ID
     * @return 是否可执行任务
     * @throws BusinessException 如果机会状态不允许执行任务
     */
    public boolean canExecuteTask(Integer opportunityId) {
        if (opportunityId == null) {
            throw new BusinessException("机会ID不能为空");
        }
        
        // 查询机会信息
        Opportunity opportunity = opportunityService.selectByPrimaryKey(opportunityId);
        if (opportunity == null) {
            throw new BusinessException("机会不存在");
        }
        
        // 检查机会是否已删除
        if (opportunity.getIsDeleted() != null && opportunity.getIsDeleted() == 1) {
            throw new BusinessException("机会已删除，无法执行任务");
        }
        
        // 检查机会状态是否允许执行任务
        Integer currentStatus = opportunity.getStatus();
        if (currentStatus == null) {
            throw new BusinessException("机会状态异常，无法执行任务");
        }
        
        // 只有已提交状态的机会可以执行任务
        if (!OpportunityStatusEnum.SUBMITTED.getCode().equals(currentStatus)) {
            String statusName = OpportunityStatusEnum.getNameByCode(currentStatus);
            throw new BusinessException("机会状态为【" + statusName + "】，无法执行任务");
        }
        
        return true;
    }

    /**
     * 根据机会ID判断当前机会状态是否可执行任务（不抛异常版本）
     * @param opportunityId 机会ID
     * @return 是否可执行任务
     */
    public boolean canExecuteTaskSilently(Integer opportunityId) {
        try {
            return canExecuteTask(opportunityId);
        } catch (BusinessException e) {
            return false;
        }
    }

    /**
     * 完成任务
     * @param opportunityId 机会ID
     * @param assignee 执行人 userId
     * @param tenantId 租户ID
     */
    public void completeTask(Integer opportunityId,String assignee,String tenantId){
        // 根据opportunityId判断当前机会状态是否可执行任务
        canExecuteTask(opportunityId);
        Opportunity opportunity = opportunityService.selectByPrimaryKey(opportunityId);
        String processInstanceId = this.selectByPrimaryKey(opportunity.getCurrentProcessId()).getBpmProcessId();
        ProcessInstanceIdVO query = new ProcessInstanceIdVO();
        query.setProcessInstanceId(processInstanceId);
        Result<List<TaskVO>> taskinfos = elmsBpmWebService.getCurrentTaskInfoById(query);
        if (EmptyUtils.isEmpty(taskinfos.getDatas())){
            throw new BusinessException("当前流程已结束");
        }
        TaskVO taskVo = taskinfos.getDatas().get(0);
        if (EmptyUtils.isNotEmpty(taskVo.getAssignee())){
            if (!SysLoginUtils.getUserId().equals(taskVo.getAssignee())){
                throw new BusinessException("当前用户非任务领取人，不得操作任务");
            }
        }

        if (taskVo.getCustomProperties()!=null){
            JSONArray dolist = JSONArray.parseArray(JSONObject.toJSONString(taskVo.getCustomProperties().get("dolist")));
            if (dolist!=null){
                for (int i = 0; i < dolist.size(); i++) {
                    ProcessDefine.DoListItem doListItem = dolist.getJSONObject(i).toJavaObject(ProcessDefine.DoListItem.class);
                    if ((DoListEnum.CONFIRM_TEAM_DIVISION.getValue()).equals(doListItem.getId())){
                        // 更新机会明细表的项目团队信息字段（二次确认分工比例）
                        List<OpportunityTeamDivisionVO> otds = opportunityTeamDivisionService.findDivision(opportunity.getTenantId(), opportunityId, 1, null);
                        if (EmptyUtils.isEmpty(otds)) {
                            throw new BusinessException("团队分工比例未配置，请配置在点击完成！");
                        }
                        break;
                    }
                }
            }
        }


        // 执行任务
        TaskCompleteVO complete = new TaskCompleteVO();
        complete.setProcessInstanceId(processInstanceId);
        complete.setAssignee(assignee);
        complete.setTaskId(taskVo.getId());
        Result<ProcessExecutionStatusVO> res = elmsBpmWebService.completeCurrentTasksByProcessInstanceId(complete);
        if (!ResultStatusEnum.isSuccess(res.getResp_code())){
            throw new BusinessException(res.getResp_msg());
        }
        //判断师傅执行到了排分接端 10
        for (TaskVO completedTask : res.getDatas().getCompletedTasks()) {
            if (completedTask.getCustomProperties()!=null && completedTask.getCustomProperties().containsKey("dolist")){
                JSONArray dolist = JSONArray.parseArray(JSONObject.toJSONString(completedTask.getCustomProperties().get("dolist")));
                if (dolist!=null){
                    for (int i = 0; i < dolist.size(); i++) {
                        ProcessDefine.DoListItem doListItem = dolist.getJSONObject(i).toJavaObject(ProcessDefine.DoListItem.class);
                        if ((DoListEnum.UPLOAD_RANKING_RESULT.getValue()).equals(doListItem.getId())){
                            opportunityTeamDivisionService.copy2Second(opportunityId,opportunity.getTenantId());
                            break;
                        }
                        if ((DoListEnum.CONFIGURE_TEAM_MEMBERS.getValue()).equals(doListItem.getId())){
                            // 更新机会明细表的组队时间字段
                            OpportunityDetail updateDetail = new OpportunityDetail();
                            updateDetail.setOpportunityId(opportunityId);
                            updateDetail.setTeamTime(new Date());
                            opportunityDetailService.updateByPrimaryKeySelective(updateDetail);
                            break;
                        }
                        if ((DoListEnum.SECOND_CONFIRM_DIVISION.getValue()).equals(doListItem.getId())){
                            // 更新机会明细表的项目团队信息字段（二次确认分工比例）
                            String formattedDivisionInfo = opportunityTeamDivisionService.getFormattedDivisionInfo(opportunityId, opportunity.getTenantId(), 2);
                            if (EmptyUtils.isNotEmpty(formattedDivisionInfo)) {
                                opportunityService.updateProjectTeamInfo(opportunityId, formattedDivisionInfo);
                            }
                            break;
                        }
                    }
                }
            }
        }

        Object stepType = taskVo.getCustomProperties().get("stepType");
        // 跟新机会步骤
        if (EmptyUtils.isNotEmpty(stepType)){
            Opportunity opp = new Opportunity();
            opp.setId(opportunityId);
            opp.setProcessStep(stepType != null ? stepType.toString() : null);
            opportunityService.updateByPrimaryKeySelective(opp);
        }

        // 记录操作日志
        OpportunityProcessLog oplog = new OpportunityProcessLog();
        oplog.setOpportunityId(opportunityId);
        oplog.setTenantId(tenantId);
        oplog.setOperatorId(assignee);
        oplog.setCreateTime(new Date());
        oplog.setIsDeleted(0);
        oplog.setIsNodeCompleteLog(1);

        StringBuffer logDesc = new StringBuffer();
        //根据assignee查询对应userId的用户信息
        User userInfo = userService.getUserInfoByUserId(assignee);
        UserTenant assigneeUserTen = userTenantService.getByUserIdAndTenantId(assignee, tenantId);
        if (EmptyUtils.isNotEmpty(taskVo.getCustomProperties().get("dolist"))){
            JSONArray dolist = JSONArray.parseArray(JSONObject.toJSONString(taskVo.getCustomProperties().get("dolist")));
            for (int i = 0; i < dolist.size(); i++) {
                if (i>0){
                    logDesc.append("<br/>");
                }
                ProcessDefine.DoListItem doListItem = dolist.getJSONObject(i).toJavaObject(ProcessDefine.DoListItem.class);
                logDesc.append(opportunityProcessLogService.buildOperatorDesc(assigneeUserTen.getOrganNamePath() + " 项目经理", userInfo.getNickName(), userInfo.getBscUseName(), "执行了", doListItem.getName(), null, null, null, null));
            }
            oplog.setOperatorDesc(logDesc.toString());
        }else{
            logDesc.append(opportunityProcessLogService.buildOperatorDesc(assigneeUserTen.getOrganNamePath() + " 项目经理", userInfo.getNickName(), userInfo.getBscUseName(), "完成了 "+taskVo.getName(), null, null, null, null, null));
            oplog.setOperatorDesc(logDesc.toString());
        }
        //北京分公司统筹 王年金（wangnj）指派机会给总公司统筹 张团财（zhangtc）
        opportunityProcessLogService.insert(oplog);
        //判断进度没走完，则设置后续人任务执行人
        if (!res.getDatas().getIsCompleted()){
            //设置后续执行人为当前项目经理
            TaskUserUpdateVO taskvO = new TaskUserUpdateVO();
            taskvO.setProcessInstanceId(processInstanceId);
            if(EmptyUtils.isNotEmpty(assignee)){
                taskvO.setAssignees(Arrays.asList(new String[]{assignee}));
            }
            log.info("taskvO:"+JSONObject.toJSONString(taskvO));
            Result<List<TaskVO>> dealRes = elmsBpmWebService.setTaskDealUserById(taskvO);
            if (!ResultStatusEnum.isSuccess(dealRes.getResp_code())){
                throw new BusinessException(dealRes.getResp_msg());
            }
        }
    }
    
    /**
     * 根据代理企业ID获取行业编码
     * @param agentEnterpriseId 代理企业ID
     * @return 行业编码
     */
    private String getIndustryCodeByAgentEnterpriseId(Integer agentEnterpriseId) {
        if (agentEnterpriseId == null) {
            return null;
        }
        try {
            GenAgentEnterprise enterprise = genAgentEnterpriseService.selectByPrimaryKey(agentEnterpriseId);
            if (enterprise != null && EmptyUtils.isNotEmpty(enterprise.getCategoryCode())) {
                return enterprise.getCategoryCode();
            }
        } catch (Exception e) {
            log.warn("获取代理企业行业编码失败，agentEnterpriseId: {}, error: {}", agentEnterpriseId, e.getMessage());
        }
        return null;
    }
}
