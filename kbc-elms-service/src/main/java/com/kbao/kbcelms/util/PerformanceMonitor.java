package com.kbao.kbcelms.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 性能监控工具类
 * 
 * <AUTHOR>
 * @since 2025-01-19
 */
@Slf4j
@Component
public class PerformanceMonitor {

    private final Map<String, Long> startTimes = new ConcurrentHashMap<>();
    private final Map<String, PerformanceMetrics> metrics = new ConcurrentHashMap<>();

    /**
     * 开始监控
     */
    public void start(String taskName) {
        startTimes.put(taskName, System.currentTimeMillis());
        log.debug("开始监控任务: {}", taskName);
    }

    /**
     * 结束监控
     */
    public long end(String taskName) {
        Long startTime = startTimes.remove(taskName);
        if (startTime == null) {
            log.warn("未找到任务开始时间: {}", taskName);
            return 0;
        }

        long duration = System.currentTimeMillis() - startTime;
        updateMetrics(taskName, duration);
        
        log.info("任务 {} 执行完成，耗时: {}ms", taskName, duration);
        return duration;
    }

    /**
     * 更新性能指标
     */
    private void updateMetrics(String taskName, long duration) {
        metrics.compute(taskName, (key, existing) -> {
            if (existing == null) {
                return new PerformanceMetrics(taskName, duration);
            } else {
                existing.addExecution(duration);
                return existing;
            }
        });
    }

    /**
     * 获取性能指标
     */
    public PerformanceMetrics getMetrics(String taskName) {
        return metrics.get(taskName);
    }

    /**
     * 获取所有性能指标
     */
    public Map<String, PerformanceMetrics> getAllMetrics() {
        return new ConcurrentHashMap<>(metrics);
    }

    /**
     * 清空指标
     */
    public void clear() {
        startTimes.clear();
        metrics.clear();
    }

    /**
     * 打印性能报告
     */
    public void printReport() {
        log.info("=== 性能监控报告 ===");
        metrics.forEach((taskName, metric) -> {
            log.info("任务: {} | 执行次数: {} | 平均耗时: {}ms | 最小耗时: {}ms | 最大耗时: {}ms | 总耗时: {}ms",
                    taskName,
                    metric.getExecutionCount(),
                    metric.getAverageTime(),
                    metric.getMinTime(),
                    metric.getMaxTime(),
                    metric.getTotalTime());
        });
        log.info("=== 报告结束 ===");
    }

    /**
     * 性能指标内部类
     */
    public static class PerformanceMetrics {
        private final String taskName;
        private long executionCount;
        private long totalTime;
        private long minTime;
        private long maxTime;

        public PerformanceMetrics(String taskName, long firstDuration) {
            this.taskName = taskName;
            this.executionCount = 1;
            this.totalTime = firstDuration;
            this.minTime = firstDuration;
            this.maxTime = firstDuration;
        }

        public void addExecution(long duration) {
            this.executionCount++;
            this.totalTime += duration;
            this.minTime = Math.min(this.minTime, duration);
            this.maxTime = Math.max(this.maxTime, duration);
        }

        public String getTaskName() {
            return taskName;
        }

        public long getExecutionCount() {
            return executionCount;
        }

        public long getTotalTime() {
            return totalTime;
        }

        public long getAverageTime() {
            return executionCount > 0 ? totalTime / executionCount : 0;
        }

        public long getMinTime() {
            return minTime;
        }

        public long getMaxTime() {
            return maxTime;
        }
    }
}
