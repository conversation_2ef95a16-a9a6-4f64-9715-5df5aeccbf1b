{"name": "kbc-elms-web", "version": "0.1.0", "private": true, "scripts": {"dev": "vue-cli-service  serve  --mode dev --open", "sta": "vue-cli-service  build  --mode sta", "uat": "vue-cli-service  build  --mode uat", "prod": "vue-cli-service build --mode prod"}, "dependencies": {"axios": "^0.24.0", "core-js": "^3.6.5", "crypto-js": "^4.2.0", "echarts": "^5.6.0", "element-ui": "2.15.6", "js-file-download": "^0.4.12", "jsplumb": "^2.15.6", "localforage": "^1.10.0", "path": "^0.12.7", "quill": "^1.3.7", "vue": "^2.6.11", "vue-router": "^3.2.0", "vuedraggable": "^2.24.3", "vuex": "^3.4.0", "vuex-along": "^1.2.13"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/eslint-config-standard": "^5.1.2", "babel-eslint": "^10.1.0", "eslint": "^6.7.2", "eslint-plugin-import": "^2.20.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.0", "eslint-plugin-vue": "^6.2.2", "less": "^3.0.4", "less-loader": "^5.0.0", "nprogress": "^0.2.0", "vue-json-viewer": "^2.2.22", "vue-template-compiler": "^2.6.11"}}