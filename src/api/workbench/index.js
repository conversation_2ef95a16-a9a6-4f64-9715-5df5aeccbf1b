import http from "@/utils/httpService";
import {
  rootPath
} from "@/utils/globalParam";



export const getMesconfigPage = (data) =>
  http.Axios.post(rootPath + "/api/mesconfig/page", data);


export const getRoomList = (data) =>
  http.Axios.post(rootPath + "/api/mesconfig/roomList", data);

export const getMeserrorPage = (data) =>
  http.Axios.post(rootPath + "/api/meserror/page", data);


export const getMesroomstuslist = (data) =>
  http.Axios.post(rootPath + "/api/mesroomstuslist/page", data);


export const getMesroomstusDetail = (data) =>
  http.Axios.post(rootPath + "/api/mesroomstuslist/detail", data);

export const checkMesroomstus = (data) =>
  http.Axios.post(rootPath + "/api/mesroomstuslist/check", data);

export const forceSubmit = (data) =>
  http.Axios.post(rootPath + "/api/mesroomstuslist/forceSubmit", data);

export const saveMsg = (data) =>
  http.Axios.post(rootPath + "/api/mesroomstuslist/saveMsg", data);


export const getMeserrorList = (data) =>
  http.Axios.post(rootPath + "/api/meserror/list", data);



export const getStsCert = data =>
  http.Axios.post(rootPath + "/api/oss/sts/getStsCert", data, {
    noLoading: true
  });

export const residualTime = data =>
  http.Axios.post(rootPath + "/api/mesconfig/residualTime", data, {
    noLoading: true
  });


export const largeScreenPage = data =>
  http.Axios.post(rootPath + "/api/mesroomstuslist/page/largeScreen", data, {
    noLoading: true
  });


export const getMqConfigInfo = (data) =>
  http.Axios.post(rootPath + "/api/mqConfigInfo/get", data);


export const getDeviceId = (data) =>
  http.Axios.get(rootPath + "/api/getDeviceId", data);


  export const getIceConfig = (data) =>
  http.Axios.post(rootPath + "/api/ice/get", data);

  export const getOrgList = (data) =>
  http.Axios.post(rootPath + "/api/bsc/getOrgList", data);


  export const getMessageInfo = (data) =>
  http.Axios.post(rootPath + "/api/mesroomstuslist/getMessageInfo", data);

  export const follow = (data) =>
  http.Axios.post(rootPath + "/api/mesroomstuslist/follow", data);


  export const commonphrasesPage = (data) =>
  http.Axios.post(rootPath + "/api/commonphrases/page", data);

  export const saveOrUpdate = (data) =>
  http.Axios.post(rootPath + "/api/commonphrases/saveOrUpdate", data);


  export const deleteCommonphrases = (data) =>
  http.Axios.post(rootPath + "/api/commonphrases/delete", data);


  export const saveMesstuVideo = (data) =>
    http.Axios.post(rootPath + "/api/messtuvideo/saveOrUpdate",data,{
      noLoading: true
    });

// 机会列表--待处理
export const toDoPage = (data) =>
  http.Axios.post(rootPath + "/api/opportunity/toDoPage", data, {
    noLoading: true
  });

// 机会列表--待领取
export const toGetPage = (data) =>
  http.Axios.post(rootPath + "/api/opportunity/toGetPage", data, {
    noLoading: true
  });

// 机会列表--待参与
export const toJoinPage = (data) =>
  http.Axios.post(rootPath + "/api/opportunity/toJoinPage", data, {
    noLoading: true
  });

// 机会列表--我参与
export const toHasJoinPage = (data) =>
  http.Axios.post(rootPath + "/api/opportunity/toHasJoinPage", data, {
    noLoading: true
  });

// 机会列表--全部机会
export const toALLPage = (data) =>
  http.Axios.post(rootPath + "/api/opportunity/toALLPage", data, {
    noLoading: true
  });

// 机会列表-- 机会状态
export const getOpportunityStatus = (data) =>
  http.Axios.post(rootPath + "/api/opportunity/getOpportunityStatus", data, {
    noLoading: true
  });

// 机会列表--参弹窗 邀请人数据获取
export const getOpportunityInviterInfo = (data) =>
  http.Axios.post(rootPath + "/api/opportunity/getOpportunityInviterInfo", data, {
    noLoading: true
  });

// 机会列表--参与或者拒绝
export const acceptOrReject = (data) =>
  http.Axios.post(rootPath + "/api/opportunity/team/acceptOrReject", data, {
    noLoading: true
  });

// 领取任务
export const acceptTask = (data) =>
  http.Axios.post(rootPath + "/api/opportunity/acceptTask", data, {
    noLoading: true
  });

// 重启机会 - 重启暂停的机会
export const resume = (data) =>
  http.Axios.post(rootPath + "/api/opportunity/resume", data, {
    noLoading: true
  });

// 重启机会 - 重启 关闭的机会
export const restart = (data) =>
  http.Axios.post(rootPath + "/api/opportunity/restart", data, {
    noLoading: true
  });

  export const addOpportunityLog = (data) =>
  http.Axios.post(rootPath + "/api/opportunity/log/add", data, {
    noLoading: true
  });


  export const deleteOpportunityLog = (data) =>
  http.Axios.post(rootPath + "/api/opportunity/log/delete", data, {
    noLoading: true
  });


  export const getOpportunityLog = (data) =>
  http.Axios.post(rootPath + "/api/opportunity/log/get", data, {
    noLoading: true
  });

  export const updateOpportunityLog = (data) =>
  http.Axios.post(rootPath + "/api/opportunity/log/update", data, {
    noLoading: true
  });


  export const getOpportunityLogList = (data) =>
  http.Axios.post(rootPath + "/api/opportunity/log/page", data, {
    noLoading: true
  });


  export const addOpportunitySummary = (data) =>
  http.Axios.post(rootPath + "/api/opportunity/summary/add", data, {
    noLoading: true
  });


  export const deleteOpportunitySummary = (data) =>
  http.Axios.post(rootPath + "/api/opportunity/summary/delete", data, {
    noLoading: true
  });


  export const getOpportunitySummaryList = (data) =>
  http.Axios.post(rootPath + "/api/opportunity/summary/page", data, {
    noLoading: true
  });


  export const closeOpportunity = (data) =>
  http.Axios.post(rootPath + "/api/opportunity/close", data, {
    noLoading: true
  });


  export const resumeOpportunity = (data) =>
  http.Axios.post(rootPath + "/api/opportunity/resume", data, {
    noLoading: true
  });

  export const suspendOpportunity = (data) =>
  http.Axios.post(rootPath + "/api/opportunity/suspend", data, {
    noLoading: true
  });


  export const setBranchCoordination = (data) =>
  http.Axios.post(rootPath + "/api/opportunity/setBranchCoordination", data, {
    noLoading: true
  });


  export const setOpportunityProjecter = (data) =>
  http.Axios.post(rootPath + "/api/opportunity/setOpportunityProjecter", data, {
    noLoading: true
  });


  export const uploadFile = (data) =>
  http.Axios.post(rootPath + "/api/upload/file", data, {
    noLoading: true,
    isImport: true
  });

  export const countUserParticipatedOpportunities = (data) =>
  http.Axios.post(rootPath + "/api/opportunityProcessLog/countUserParticipatedOpportunities", data, {
    noLoading: true
  });



  export const restartOpportunity = (data) =>
  http.Axios.post(rootPath + "/api/opportunity/restart", data, {
    noLoading: true
  });


  export const getCloseReason = (data) =>
  http.Axios.post(rootPath + "/api/opportunityProcessLog/getCloseReason", data, {
    noLoading: true
  });

  export const getOpportunityTimeline = (data) =>
  http.Axios.post(rootPath + "/api/opportunity/timeline", data, {
    noLoading: true
  });

  export const getOpportunityProgressLog = (data) =>
  http.Axios.post(rootPath + "/api/opportunity/progesslog", data, {
    noLoading: true
  });

  export const getOpportunityProcessProgress = (data) =>
  http.Axios.post(rootPath + "/api/opportunity/process/progress", data, {
    noLoading: true
  });

  export const getOpportunityDetailFull = (data) =>
  http.Axios.post(rootPath + "/api/opportunity/detail/full", data, {
    noLoading: true
  });


  export const completeTask = (data) =>
  http.Axios.post(rootPath + "/api/opportunity/completeTask", data, {
    noLoading: false
  });


  export const findLegalOrgDataByTenantId = (data) =>
  http.Axios.post(rootPath + "/api/user/findLegalOrgDataByTenantId", data, {
    noLoading: true
  });


export const changeManager = (data) =>
  http.Axios.post(rootPath + "/api/opportunity/team/manager/change", data, {
    noLoading: true
  });











// 项目成员查询
export const memberList = (data) =>
  http.Axios.post(rootPath + "/api/opportunity/team/list", data, {
    noLoading: true
});

// 查询可新增的项目成员
export const branchUserList = (data) =>
  http.Axios.post(rootPath + "/api/user/branchUsers", data, {
    noLoading: true
});


// 新增项目成员
export const addTeamMember = (data) =>
  http.Axios.post(rootPath + "/api/opportunity/team/add", data, {
    noLoading: true
});

// 删除项目成员
export const removeTeamMember = (data) =>
  http.Axios.post(rootPath + "/api/opportunity/team/remove", data, {
    noLoading: true
});

// 项目成员邀请确认
export const inviteTeamMember = (data) =>
  http.Axios.post(rootPath + "/api/opportunity/team/invite", data, {
    noLoading: true
});

// 项目分工列表
export const divisionList = (data) =>
  http.Axios.post(rootPath + "/api/opportunity/team/division/list", data, {
    noLoading: true
});

// 分工配置列表
export const divisionRatioList = (data) =>
  http.Axios.post(rootPath + "/api/division/ratio/list", data, {
    noLoading: true
});

// 新增项目分工
export const addDivisionRatio = (data) =>
  http.Axios.post(rootPath + "/api/opportunity/team/division/add", data, {
    noLoading: true
  });

// 修改项目分工
export const editDivisionRatio = (data) =>
  http.Axios.post(rootPath + "/api/opportunity/team/division/update", data, {
    noLoading: true
  });

// 发送确认项目分工
export const sendDivisionNotice = (data) =>
  http.Axios.post(rootPath + "/api/opportunity/team/division/send", data, {
    noLoading: true
  });

// 保险公司列表
export const getCompnayList = (data) =>
  http.Axios.post(rootPath + "/api/bsc/getCompanyList", data, {
    noLoading: true
  });

// 批量保存保单号
export const batchAddOrders = (data) =>
  http.Axios.post(rootPath + "/api/opportunity/order/batch/add", data, {
    noLoading: true
  });

// 查询保单号
export const insureOrderList = (data) =>
  http.Axios.post(rootPath + "/api/opportunity/order/list", data, {
    noLoading: true
  });

// 删除保单号
export const deleteOrder = (data) =>
  http.Axios.post(rootPath + "/api/opportunity/order/delete", data, {
    noLoading: true
  });

// 修改保单号
export const updateOrder = (data) =>
  http.Axios.post(rootPath + "/api/opportunity/order/update", data, {
    noLoading: true
});

// 获取生态产品列表
export const esptProductList = (data) =>
  http.Axios.post(rootPath + "/api/espt/product/list", data, {
    noLoading: true
  });

// 获取生态产品详情
export const esptProductInfo = (data) =>
  http.Axios.post(rootPath + "/api/espt/product/get", data, {
    noLoading: true
});

// 获取生态产品服务详情
export const esptProductItemInfo = (data) =>
  http.Axios.post(rootPath + "/api/espt/product/item/get", data, {
    noLoading: true
  });

// 机会明细获取
export const getDetail = (data) =>
  http.Axios.post(rootPath + "/api/opportunity/detail/get", data, {
    noLoading: true
  });

// 机会明细保存
export const detailSave = (data) =>
  http.Axios.post(rootPath + "/api/opportunity/detail/save", data, {
    noLoading: true
  });

// 机会资料查询
export const fileList = (data) =>
  http.Axios.post(rootPath + "/api/opportunity/file/list", data, {
    noLoading: true
  });

// 机会资料上传
export const fileUpload = (data) =>
  http.Axios.post(rootPath + "/api/opportunity/file/upload", data, {
    noLoading: true
  });
// 机会资料删除
export const fileRemove = (data) =>
  http.Axios.post(rootPath + "/api/opportunity/file/remove", data, {
    noLoading: true
  });

// 判断当前用户是否是项目经理
export const checkManager = (data) =>
  http.Axios.post(rootPath + "/api/opportunity/team/manager/check", data, {
    noLoading: true
  });

// 确认分工比例
export const divisionAcceptOrReject = (data) =>
  http.Axios.post(rootPath + "/api/opportunity/team/division/acceptOrReject", data, {
    noLoading: true
  });

// 项目详情分页
export const detailPage = (data) =>
  http.Axios.post(rootPath + "/api/opportunity/detail/page", data, {
    noLoading: true
  });



