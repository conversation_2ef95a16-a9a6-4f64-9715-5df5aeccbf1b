<template>
  <EditPageContainer
    title="企业确权详情"
    icon="el-icon-s-check"
    :breadcrumb-items="breadcrumbItems"
    :loading="loading"
    :isView="true"
    @back="goBack"
  >
    <div v-loading="loading">
      <!-- 企业基本信息 -->
      <el-card class="detail-card" shadow="never">
        <div slot="header" class="card-header">
          <span>企业基本信息</span>
        </div>
        <div class="info-grid" v-if="detailData.enterpriseBasicInfo">
          <div class="info-item">
            <label>企业名称：</label>
            <span>{{ detailData.enterpriseBasicInfo.name || '-' }}</span>
          </div>
          <div class="info-item">
            <label>统一信用代码：</label>
            <span>{{ detailData.enterpriseBasicInfo.creditCode || '-' }}</span>
          </div>
          <div class="info-item">
            <label>所在地：</label>
            <span>{{ detailData.enterpriseBasicInfo.city || '-' }}</span>
          </div>
          <div class="info-item">
            <label>人员规模：</label>
            <span>{{ detailData.enterpriseBasicInfo.staffScale || '-' }}</span>
          </div>
          <div class="info-item">
            <label>营业收入：</label>
            <span>{{ detailData.enterpriseBasicInfo.annualIncome || '-' }}</span>
          </div>
          <div class="info-item">
            <label>企业规模：</label>
            <span>{{ detailData.enterpriseBasicInfo.scale || '-' }}</span>
          </div>
        </div>
        <div v-else class="no-data">
          <span>暂无企业基本信息</span>
        </div>
      </el-card>

      <!-- 创建企业时的录入信息 -->
      <el-card class="detail-card" shadow="never">
        <div slot="header" class="card-header">
          <span>创建企业时的录入信息</span>
        </div>
        <div class="info-grid" v-if="detailData.genAgentEnterprise">
          <div class="info-item">
            <label>创建人：</label>
            <span>{{ detailData.genAgentEnterprise.creator || '-' }}</span>
          </div>
          <div class="info-item">
            <label>创建时间：</label>
            <span>{{ detailData.genAgentEnterprise.createTime || '-' }}</span>
          </div>
          <div class="info-item">
            <label>区域中心：</label>
            <span>{{ detailData.genAgentEnterprise.areaCenterName || '-' }}</span>
          </div>
          <div class="info-item">
            <label>营业部：</label>
            <span>{{ detailData.genAgentEnterprise.tradingCenterName || '-' }}</span>
          </div>
          <div class="info-item">
            <label>所属团队：</label>
            <span>{{ detailData.genAgentEnterprise.bpGroupName || '-' }}</span>
          </div>
          <div class="info-item">
            <label>企业名称：</label>
            <span>{{ detailData.genAgentEnterprise.name || '-' }}</span>
          </div>
          <div class="info-item">
            <label>统一信用代码：</label>
            <span>{{ detailData.genAgentEnterprise.creditCode || '-' }}</span>
          </div>
          <div class="info-item">
            <label>企业类型：</label>
            <span>{{ detailData.genAgentEnterprise.dtType || '-' }}</span>
          </div>
          <div class="info-item">
            <label>企业规模：</label>
            <span>{{ detailData.genAgentEnterprise.enterpriseScale || '-' }}</span>
          </div>
          <div class="info-item">
            <label>人员规模：</label>
            <span>{{ detailData.genAgentEnterprise.staffScale || '-' }}</span>
          </div>
          <div class="info-item">
            <label>所在城市：</label>
            <span>{{ detailData.genAgentEnterprise.city || '-' }}</span>
          </div>
          <div class="info-item">
            <label>营业收入：</label>
            <span>{{ detailData.genAgentEnterprise.annualIncome || '-' }}</span>
          </div>
          <div class="info-item">
            <label>行业分类：</label>
            <span>{{ detailData.genAgentEnterprise.categoryName || '-' }}</span>
          </div>
          <div class="info-item">
            <label>企业联系人：</label>
            <span>{{ detailData.genAgentEnterprise.enterpriseContacter || '-' }}</span>
          </div>
          <div class="info-item">
            <label>联系人电话：</label>
            <span>{{ detailData.genAgentEnterprise.contacterPhone || '-' }}</span>
          </div>
          <div class="info-item">
            <label>备注：</label>
            <span>{{ detailData.genAgentEnterprise.remark || '-' }}</span>
          </div>
<!--          <div class="info-item">-->
<!--            <label>是否验真：</label>-->
<!--            <span>{{ detailData.genAgentEnterprise.isVerified === '1' ? '已验真' : '未验真' }}</span>-->
<!--          </div>-->
        </div>
        <div v-else class="no-data">
          <span>暂无录入信息</span>
        </div>
      </el-card>

      <!-- 重复企业创建人信息列表 -->
      <el-card class="detail-card" shadow="never">
        <div slot="header" class="card-header">
          <span>重复企业创建人信息</span>
        </div>
        <div v-if="detailData.duplicateCreators && detailData.duplicateCreators.length > 0">
          <div v-for="(creator, index) in detailData.duplicateCreators" :key="index" class="creator-item">
            <!-- 基本信息 -->
            <div class="creator-basic-info">
              <div class="info-row">
                <div class="info-item">
                  <label>企业创建人：</label>
                  <span class="creator-name">{{ creator.creator || '-' }}</span>
                </div>
                <div class="info-item">
                  <label>创建时间：</label>
                  <span>{{ creator.createTime || '-' }}</span>
                </div>
              </div>
              <div class="info-row">
                <div class="info-item">
                  <label>区域中心：</label>
                  <span>{{ creator.areaCenterName || '-' }}</span>
                </div>
                <div class="info-item">
                  <label>营业部：</label>
                  <span>{{ creator.tradingCenterName || '-' }}</span>
                </div>
              </div>
              <div class="info-row">
                <div class="info-item">
                  <label>所属团队：</label>
                  <span>{{ creator.bpGroupName || '-' }}</span>
                </div>
              </div>
            </div>

            <!-- 机会统计 - 只有当机会总数大于0时才显示 -->
            <div v-if="creator.opportunityNum > 0" class="opportunity-stats">
              <div class="stats-row">
                <div class="stat-item">
                  <div class="stat-label">机会总数</div>
                  <div class="stat-value">{{ creator.opportunityNum || 0 }}条</div>
                </div>
                <div class="stat-item">
                  <div class="stat-label">进行中机会</div>
                  <div class="stat-value">{{ creator.underwayInsureTypes || '-' }}</div>
                </div>
                <div class="stat-item">
                  <div class="stat-label">已锁定机会</div>
                  <div class="stat-value">{{ creator.lockInsureTypes || '-' }}</div>
                </div>
                <div class="stat-item">
                  <div class="stat-label">已出单</div>
                  <div class="stat-value">{{ creator.issuedInsureTypes || '-' }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-else class="no-data">
          <span>暂无重复企业创建人信息</span>
        </div>
      </el-card>

      <!-- 重复企业处理记录列表 -->
      <el-card class="detail-card" shadow="never">
        <div slot="header" class="card-header">
          <span>重复企业处理记录</span>
          <el-button
            v-if="!isProcessed"
            type="primary"
            size="small"
            @click="showProcessDialog"
            style="float: right;"
          >
            处理
          </el-button>
        </div>
        <el-table :data="detailData.processRecords" border v-if="detailData.processRecords && detailData.processRecords.length > 0">
          <el-table-column prop="userName" label="处理人" align="center" />
          <el-table-column prop="remark" label="批注" align="center" show-overflow-tooltip />
          <el-table-column prop="processTime" label="处理时间" align="center" />
          <el-table-column prop="legalName" label="处理机构" align="center" />
        </el-table>
        <div v-else class="no-data">
          <span>暂无处理记录</span>
        </div>
      </el-card>
    </div>

    <!-- 处理弹窗 -->
    <el-dialog
      title="处理重复企业记录"
      :visible.sync="processDialogVisible"
      width="600px"
      :close-on-click-modal="false"
      @close="handleProcessDialogClose"
    >
      <div class="process-form">
        <div class="form-item">
          <label class="form-label">处理备注：</label>
          <div class="form-content">
            <div ref="processRemarkEditor" class="rich-editor"></div>
          </div>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="processDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleProcessConfirm" :loading="processLoading">确认</el-button>
      </div>
    </el-dialog>
  </EditPageContainer>
</template>

<script>
import { getConfirmationDetail, saveProcessRecord } from '@/api/enterprise/confirmation'
import EditPageContainer from '@/components/layouts/EditPageContainer.vue'

export default {
  name: 'EnterpriseConfirmationDetail',
  components: {
    EditPageContainer
  },
  data() {
    return {
      loading: false,
      detailData: {
        enterpriseBasicInfo: null,
        genAgentEnterprise: null,
        duplicateCreators: [],
        processRecords: []
      },
      breadcrumbItems: [
        { text: '企业确权管理', to: 'EnterpriseConfirmation' },
        { text: '企业确权详情' }
      ],

      // 处理弹窗相关
      processDialogVisible: false,
      processLoading: false,
      processRemark: '',
      richEditor: null,
      isProcessed: false
    }
  },

  created() {
    this.loadDetail()
  },

  methods: {
    async loadDetail() {
      this.loading = true
      try {
        const id = this.$route.params.id
        const response = await getConfirmationDetail(id)
        console.log('response:', response)
        this.detailData = response || {}

        // 设置处理状态
        this.isProcessed = this.detailData.isProcessed === 1
      } catch (error) {
        console.error('加载详情失败:', error)
        this.$message.error('加载详情失败')
      } finally {
        this.loading = false
      }
    },

    goBack() {
      this.$router.go(-1)
    },

    // 显示处理弹窗
    showProcessDialog() {
      this.processRemark = ''
      this.processDialogVisible = true

      // 初始化富文本编辑器
      this.$nextTick(() => {
        this.initRichEditor()
      })
    },

    // 初始化富文本编辑器
    initRichEditor() {
      if (this.richEditor) {
        this.richEditor.destroy()
      }

      // 使用简单的富文本编辑器
      const editorElement = this.$refs.processRemarkEditor
      if (editorElement) {
        editorElement.innerHTML = '<div contenteditable="true" style="min-height: 150px; padding: 10px; border: 1px solid #dcdfe6; border-radius: 4px; outline: none;" placeholder="请输入处理备注..."></div>'

        const editableDiv = editorElement.querySelector('[contenteditable]')
        editableDiv.addEventListener('input', (e) => {
          this.processRemark = e.target.innerHTML
        })
      }
    },

    // 处理弹窗关闭
    handleProcessDialogClose() {
      this.processRemark = ''
      if (this.richEditor) {
        this.richEditor.destroy()
        this.richEditor = null
      }
    },

    // 确认处理
    async handleProcessConfirm() {
      if (!this.processRemark || this.processRemark.trim() === '') {
        this.$message.warning('请输入处理备注')
        return
      }

      this.processLoading = true
      try {
        const id = this.$route.params.id
        await saveProcessRecord({
          confirmationId: parseInt(id),
          processRemark: this.processRemark
        })

        this.$message.success('处理成功')
        this.processDialogVisible = false
        this.isProcessed = true
        this.loadDetail() // 重新加载数据
      } catch (error) {
        console.error('处理失败:', error)
        this.$message.error('处理失败')
      } finally {
        this.processLoading = false
      }
    }
  }
}
</script>

<style lang="less" scoped>
.detail-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  margin-bottom: 24px;
  overflow: hidden;
  border: 1px solid #eaedf2;

  /deep/ .el-card__header {
    background: #f7ecdd;
    padding: 16px 24px;
    border-bottom: 1px solid #f0e6d0;
  }

  /deep/ .el-card__body {
    padding: 24px;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 600;
    color: #2c3e50;
    font-size: 18px;

    .card-subtitle {
      font-size: 12px;
      color: #999;
      font-weight: normal;
    }
  }
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }

  label {
    font-weight: 500;
    min-width: 120px;
    color: #666;
    font-size: 14px;
  }

  span {
    color: #333;
    font-size: 14px;
  }
}

// 重复企业创建人信息样式
.creator-item {
  border: 1px solid #eaedf2;
  border-radius: 12px;
  margin-bottom: 20px;
  background: #fafafa;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);

  &:last-child {
    margin-bottom: 0;
  }
}

.creator-basic-info {
  padding: 20px;
  background: white;
  border-radius: 12px 12px 0 0;

  .info-row {
    display: flex;
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }

    .info-item {
      flex: 1;
      display: flex;
      align-items: center;

      label {
        font-weight: 500;
        min-width: 100px;
        color: #666;
        font-size: 14px;
      }

      span {
        color: #333;
        font-size: 14px;

        &.creator-name {
          color: #1890ff;
          font-weight: 500;
        }
      }
    }
  }
}

.opportunity-stats {
  padding: 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 0 0 12px 12px;
  border-top: 1px solid #eaedf2;

  .stats-row {
    display: flex;
    justify-content: space-around;

    .stat-item {
      text-align: center;
      flex: 1;

      .stat-label {
        font-size: 12px;
        color: #666;
        margin-bottom: 6px;
      }

      .stat-value {
        font-size: 14px;
        font-weight: 500;
        color: #333;

        &:not(:last-child) {
          border-right: 1px solid #ddd;
        }
      }
    }
  }
}

.no-data {
  text-align: center;
  color: #999;
  padding: 60px 0;
  font-size: 14px;
}

// 处理弹窗样式
.process-form {
  .form-item {
    margin-bottom: 24px;

    .form-label {
      display: block;
      margin-bottom: 12px;
      font-weight: 500;
      color: #606266;
      font-size: 14px;
    }

    .form-content {
      .rich-editor {
        border: 1px solid #dcdfe6;
        border-radius: 6px;
        min-height: 150px;

        div[contenteditable] {
          &:empty:before {
            content: attr(placeholder);
            color: #c0c4cc;
          }

          &:focus {
            border-color: #409eff;
            box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
          }
        }
      }
    }
  }
}
</style>
