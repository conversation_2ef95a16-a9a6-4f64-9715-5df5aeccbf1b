<template>
  <EditPageContainer
    :title="pageTitle"
    :icon="pageIcon"
    :breadcrumb-items="breadcrumbItems"
    :loading="loading"
    :is-view="isView"
    @back="handleBack"
    @save="handleSave"
    @breadcrumb-click="handleBreadcrumbClick"
  >
    <UniversalForm
      ref="universalForm"
      :formData="form"
      :formRules="formRules"
      :formGroups="formGroups"
      :labelWidth="'120px'"
      :rowGutter="20"
      :isView="isView"
    />
  </EditPageContainer>
</template>

<script>
import { getBusinessProcessList } from '@/api/businessProcess/index.js'
import { getIndustryLimitRuleDetail, getFieldOptions, createIndustryLimitRule, updateIndustryLimitRule } from '@/api/industryLimit'
import EditPageContainer from '@/components/layouts/EditPageContainer.vue'
import UniversalForm from '@/components/layouts/UniversalForm.vue'

export default {
  name: 'IndustryLimitConfig',
  components: { EditPageContainer, UniversalForm },
  data() {
    return {
      loading: false,
      isView: false,
      form: {
        name: '',
        id: '',
        description: '',
        conditions: [],
        actionList: []
      },
      formGroups: [
        {
          title: '基本信息',
          icon: 'el-icon-info',
          fields: [
            [
              { prop: 'name', label: '规则名称', type: 'input', placeholder: '请输入规则名称', maxlength: 50, showWordLimit: true },
              { prop: 'id', label: '规则ID', type: 'input', placeholder: '自动生成/可编辑', maxlength: 20 }
            ],
            [
              { prop: 'description', label: '描述', type: 'textarea', placeholder: '请输入描述', maxlength: 200, rows: 3, showWordLimit: true, span: 24 }
            ]
          ]
        },
        {
          title: '字段规则',
          icon: 'el-icon-s-operation',
          fields: [
            [
              {
                type: 'list',
                prop: 'conditions',
                label: '字段规则',
                icon: 'el-icon-s-operation',
                addText: '添加条件',
                min: 0,
                defaultRow: () => ({ field: '', operator: '', value: '', minValue: '', maxValue: '' }),
                columns: [
                  {
                    prop: 'field',
                    label: '字段',
                    type: 'select',
                    width: 160,
                    options: [],
                    onChange: (value, row, index, formData) => {
                      row.value = ''
                      // 更新操作符选项
                      this.updateOperatorOptions(row)
                    }
                  },
                  {
                    prop: 'operator',
                    label: '操作符',
                    type: 'select',
                    width: 120,
                    options: [],
                    onChange: (value, row, index, formData) => {
                      // 清空值字段
                      row.value = ''
                      row.minValue = ''
                      row.maxValue = ''
                    }
                  },
                  {
                    prop: 'value',
                    label: '值',
                    type: 'custom',
                    minWidth: 200,
                    render: (h, { row, index }) => {
                      return this.renderValueInput(h, row, index)
                    }
                  }
                ],
                actions: [
                  {
                    label: '删除',
                    icon: 'el-icon-delete',
                    type: 'danger',
                    onClick: (row, index, formData) => {
                      formData.conditions.splice(index, 1)
                    }
                  }
                ]
              }
            ]
          ]
        },
        {
          title: '执行动作',
          icon: 'el-icon-s-custom',
          fields: [
            [
              {
                type: 'list',
                prop: 'actionList',
                label: '执行动作',
                icon: 'el-icon-s-custom',
                addText: '添加动作',
                min: 0,
                defaultRow: () => ({ type: 'applyRule', serviceIds: [], ruleName: '' }),
                columns: [
                  {
                    prop: 'type',
                    label: '动作类型',
                    type: 'radio',
                    width: 200,
                    options: [
                      { label: '执行', value: 'applyRule' },
                      { label: '不执行', value: 'noAction' }
                    ]
                  },
                  {
                    prop: 'serviceIds',
                    label: '启用服务',
                    type: 'select',
                    minWidth: 180,
                    multiple: true,
                    placeholder: '请选择启用的企业服务',
                    options: []
                  }
                ],
                actions: [
                  {
                    label: '删除',
                    icon: 'el-icon-delete',
                    type: 'danger',
                    onClick: (row, index, formData) => {
                      formData.actionList.splice(index, 1)
                    }
                  }
                ]
              }
            ]
          ]
        }
      ],
      formRules: {
        name: [{ required: true, message: '请输入规则名称', trigger: 'blur' }],
        description: [{ required: true, message: '请输入描述', trigger: 'blur' }]
      },
      fieldOptions: [],
      serviceOptions: []
    }
  },
  computed: {
    pageTitle() {
      return this.$route.params.id ? '编辑规则' : '新增规则'
    },
    pageIcon() {
      return 'el-icon-edit'
    },
    breadcrumbItems() {
      return [
        { text: '行业限制管理', to: { name: 'industryLimit' }, icon: 'el-icon-s-custom' },
        { text: this.pageTitle, icon: this.pageIcon }
      ]
    }
  },
  created() {
    // 先加载字段选项和服务选项
    Promise.all([
      this.loadFieldOptions(),
      this.loadServiceOptions()
    ]).then(() => {
      // 然后再加载规则详情
      this.loadRule()
    })
    this.isView = this.$route.query.mode === 'view';
  },
  methods: {
    async loadRule() {
      const ruleId = this.$route.params.id
      if (!ruleId) return // 新增模式不需要加载数据
      
      this.loading = true
      try {
        const response = await getIndustryLimitRuleDetail(ruleId)
        console.log('规则详情响应:', response) // 调试日志
        const rule = response
        this.form = {
          name: rule.name,
          id: rule.id,
          description: rule.description,
          conditions: rule.conditions ? rule.conditions.map(c => {
            const condition = { ...c };
            // 如果是区间值，解析minValue和maxValue
            if (condition.operator === 'range' && condition.value && condition.value.includes('-')) {
              const [minValue, maxValue] = condition.value.split('-');
              condition.minValue = minValue;
              condition.maxValue = maxValue;
            } else {
              condition.minValue = condition.minValue || '';
              condition.maxValue = condition.maxValue || '';
            }
            return condition;
          }) : [],
          actionList: rule.actionList ? rule.actionList.map(a => ({
            ...a,
            serviceIds: Array.isArray(a.serviceIds)
              ? a.serviceIds.map(id => String(id))
              : (a.serviceIds ? String(a.serviceIds).split(',') : [])
          })) : []
        }
        
        // 初始化条件的操作符选项
        this.$nextTick(() => {
          this.initializeConditionOperators();
        });
      } catch (error) {
        console.error('加载规则失败:', error)
        this.$message.error('加载规则失败：' + error.message)
      } finally {
        this.loading = false
      }
    },
    async loadFieldOptions() {
      try {
        // 直接使用getFieldOptions API获取字段选项，而不是从数据模板中获取
        const res = await getFieldOptions();
        console.log('字段选项响应:', res) // 调试日志
        
        // 直接使用响应数据
        this.fieldOptions = res;
        
        // 更新表单配置中的字段选项
        const fieldGroup = this.formGroups.find(group => group.title === '字段规则');
        if (fieldGroup) {
          const fieldField = fieldGroup.fields[0][0];
          fieldField.columns[0].options = this.fieldOptions.map(f => ({ label: f.name, value: f.code }));
        }
      } catch (error) {
        console.error('加载字段选项失败:', error);
      }
    },
    
    // 根据字段的showType获取操作符选项
    getOperatorOptions(showType) {
      const baseOptions = [
        { label: '等于', value: 'eq' }
      ];
      
      switch (showType) {
        case '1': // 输入框     
          return [
            ...baseOptions,
            { label: '包含', value: 'contains' },
            { label: '大于', value: 'greater' },
            { label: '大于等于', value: 'greaterOrEqual' },
            { label: '小于', value: 'less' },
            { label: '小于等于', value: 'lessOrEqual' },
            { label: '区间', value: 'range' }
          ];
        case '2': // 单选框
        case '3': // 下拉框
          return [
            ...baseOptions,
            { label: '包含', value: 'in' }
          ];
        case '6': // 文本域
          return [
            ...baseOptions,
            { label: '包含', value: 'contains' },
            { label: '区间', value: 'range' }
          ];
        case '4': // 日期选择
          return [
            ...baseOptions,
            { label: '区间', value: 'range' }
          ];
        case '5': // 日期范围
          return [
            ...baseOptions,
            { label: '区间', value: 'range' }
          ];
        default:
          return baseOptions;
      }
    },
    
    // 更新操作符选项
     updateOperatorOptions(row) {
       const field = this.fieldOptions.find(f => f.code === row.field);
       if (field) {
         const fieldGroup = this.formGroups.find(group => group.title === '字段规则');
         if (fieldGroup) {
           const operatorColumn = fieldGroup.fields[0][0].columns[1];
           operatorColumn.options = this.getOperatorOptions(field.showType);
           // 清空操作符选择
           row.operator = '';
         }
       }
     },
    
    // 渲染值输入组件
    renderValueInput(h, row, index) {
      const field = this.fieldOptions.find(f => f.code === row.field);
      if (!field) {
        return h('el-input', {
          props: {
            value: row.value,
            placeholder: '请先选择字段'
          },
          attrs: { disabled: true }
        });
      }
      
      const showType = field.showType;
      const operator = row.operator;
      
      // 区间输入
      if (operator === 'range') {
        return this.renderRangeInput(h, row, showType);
      }
      
      // 根据showType渲染不同的输入组件
      switch (showType) {
        case '1': // 输入框
        case '6': // 文本域
          return this.renderTextInput(h, row);
        case '2': // 单选框
        case '3': // 下拉框
          return this.renderSelectInput(h, row, field);
        case '4': // 日期选择
          return this.renderDateInput(h, row);
        case '5': // 日期范围
          return this.renderDateRangeInput(h, row);
        default:
          return this.renderTextInput(h, row);
      }
    },
    
    // 渲染文本输入
    renderTextInput(h, row) {
      return h('el-input', {
        props: {
          value: row.value,
          placeholder: '请输入值'
        },
        on: {
          input: (value) => {
            row.value = value;
          }
        }
      });
    },
    
    // 渲染选择输入
     renderSelectInput(h, row, field) {
       // 根据field的additional属性获取选项，如果没有则提供默认选项
       let options = [];
       if (field.additional && field.additional.selectOptions) {
         options = field.additional.selectOptions;
       } else if (field.additional && typeof field.additional === 'string') {
         try {
           const additionalObj = JSON.parse(field.additional);
           options = additionalObj.selectOptions || [];
         } catch (e) {
           console.warn('解析字段additional属性失败:', e);
           options = [];
         }
       }
       
       // 如果没有选项，则使用文本输入
       if (options.length === 0) {
         return this.renderTextInput(h, row);
       }
       
       // 判断是否为包含操作符，需要多选
       const isMultiple = row.operator === 'in';
       
       // 根据showType渲染不同的组件
       if (field.showType === '2') {
         // 单选框 - 如果是包含操作符，使用多选框组
         if (isMultiple) {
           const selectedValues = Array.isArray(row.value) ? row.value : (row.value ? row.value.split(',') : []);
           return h('el-checkbox-group', {
             props: {
               value: selectedValues
             },
             on: {
               input: (val) => {
                 row.value = val.join(',');
               }
             }
           }, options.map(option => 
             h('el-checkbox', { props: { label: option.code } }, option.value)
           ));
         } else {
           return h('el-radio-group', {
             props: {
               value: row.value
             },
             on: {
               input: (val) => {
                 row.value = val;
               }
             }
           }, options.map(option => 
             h('el-radio', { props: { label: option.code } }, option.value)
           ));
         }
       } else {
         // 下拉框
         const selectedValue = isMultiple ? 
           (Array.isArray(row.value) ? row.value : (row.value ? row.value.split(',') : [])) : 
           row.value;
         
         return h('el-select', {
           props: {
             value: selectedValue,
             placeholder: isMultiple ? '请选择值（可多选）' : '请选择值',
             clearable: true,
             filterable: true,
             multiple: isMultiple
           },
           on: {
             change: (value) => {
               if (isMultiple) {
                 row.value = Array.isArray(value) ? value.join(',') : value;
               } else {
                 row.value = value;
               }
             }
           }
         }, options.map(option => 
           h('el-option', {
             props: {
               label: option.value,
               value: option.code
             }
           })
         ));
       }
     },
    
    // 渲染日期输入
    renderDateInput(h, row) {
      return h('el-date-picker', {
        props: {
          value: row.value,
          type: 'date',
          placeholder: '请选择日期',
          format: 'yyyy-MM-dd',
          valueFormat: 'yyyy-MM-dd'
        },
        on: {
          change: (value) => {
            row.value = value;
          }
        }
      });
    },
    
    // 渲染日期范围输入
    renderDateRangeInput(h, row) {
      return h('el-date-picker', {
        props: {
          value: row.value ? row.value.split(',') : [],
          type: 'daterange',
          rangeSeparator: '至',
          startPlaceholder: '开始日期',
          endPlaceholder: '结束日期',
          format: 'yyyy-MM-dd',
          valueFormat: 'yyyy-MM-dd'
        },
        on: {
          change: (value) => {
            row.value = value ? value.join(',') : '';
          }
        }
      });
    },
    
    // 渲染区间输入
    renderRangeInput(h, row, showType) {
      const isDateType = showType === '4' || showType === '5';
      
      return h('div', {
        style: {
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          gap: '8px',
          width: '100%',
          minHeight: '32px'
        }
      }, [
        // 最小值输入
        isDateType ? 
          h('el-date-picker', {
            props: {
              value: row.minValue,
              type: 'date',
              placeholder: '最小值',
              format: 'yyyy-MM-dd',
              valueFormat: 'yyyy-MM-dd',
              size: 'small'
            },
            style: { width: '120px' },
            on: {
              change: (value) => {
                row.minValue = value;
                this.updateRangeValue(row);
              }
            }
          }) :
          h('el-input', {
            props: {
              value: row.minValue,
              placeholder: '最小值',
              size: 'small'
            },
            style: { width: '80px' },
            on: {
              input: (value) => {
                row.minValue = value;
                this.updateRangeValue(row);
              }
            }
          }),
        
        // 分隔符
        h('span', '~'),
        
        // 最大值输入
        isDateType ? 
          h('el-date-picker', {
            props: {
              value: row.maxValue,
              type: 'date',
              placeholder: '最大值',
              format: 'yyyy-MM-dd',
              valueFormat: 'yyyy-MM-dd',
              size: 'small'
            },
            style: { width: '120px' },
            on: {
              change: (value) => {
                row.maxValue = value;
                this.updateRangeValue(row);
              }
            }
          }) :
          h('el-input', {
            props: {
              value: row.maxValue,
              placeholder: '最大值',
              size: 'small'
            },
            style: { width: '80px' },
            on: {
              input: (value) => {
                row.maxValue = value;
                this.updateRangeValue(row);
              }
            }
          })
      ]);
    },
    
    // 更新区间值
     updateRangeValue(row) {
       if (row.minValue && row.maxValue) {
         row.value = `${row.minValue}-${row.maxValue}`;
       } else {
         row.value = '';
       }
     },
     
     // 初始化条件的操作符选项
     initializeConditionOperators() {
       if (this.form.conditions && this.form.conditions.length > 0) {
         this.form.conditions.forEach(condition => {
           if (condition.field) {
             const field = this.fieldOptions.find(f => f.code === condition.field);
             if (field) {
               const fieldGroup = this.formGroups.find(group => group.title === '字段规则');
               if (fieldGroup) {
                 const operatorColumn = fieldGroup.fields[0][0].columns[1];
                 operatorColumn.options = this.getOperatorOptions(field.showType);
               }
             }
           }
         });
       }
     },
    async loadServiceOptions() {
      try {
        const res = await getBusinessProcessList()
        console.log('业务流程响应:', res) // 调试日志
        
        // 解析业务流程接口返回的数据结构
        let serviceOptions = []
        if (res && res.datas && Array.isArray(res.datas)) {
          serviceOptions = res.datas
        } else if (res && Array.isArray(res)) {
          serviceOptions = res
        } else {
          console.warn('业务流程数据格式不符合预期:', res)
          serviceOptions = []
        }
        
        this.serviceOptions = serviceOptions
        
        // 更新表单配置中的服务选项
        const actionGroup = this.formGroups.find(group => group.title === '执行动作');
        if (actionGroup) {
          const actionField = actionGroup.fields[0][0];
          // 根据实际数据结构映射选项
          actionField.columns[1].options = this.serviceOptions.map(s => ({
            label: s.businessName || s.name || s.processName || s.title,
            value: String(s.businessCode || s.id || s.processId)
          }));
        }
      } catch (error) {
        console.error('加载业务流程选项失败:', error);
        this.$message.error('加载业务流程选项失败');
      }
    },
    async handleSave() {
      try {
        await this.$refs.universalForm.validate()
        this.loading = true
        
        const ruleId = this.$route.params.id
        let response
        
        if (ruleId) {
          // 更新
          response = await updateIndustryLimitRule(this.form)
        } else {
          // 新增
          response = await createIndustryLimitRule(this.form)
        }
        
        console.log('保存响应:', response) // 调试日志
        
        // 检查响应结构（直接响应）
        this.$message.success('保存成功')
        
        // 保存成功后跳转回列表页面
        setTimeout(() => {
          this.$router.push({ name: 'industryLimit' })
        }, 1000)
        
      } catch (error) {
        this.$message.error('保存失败：' + error.message)
        console.error('保存失败:', error)
      } finally {
        this.loading = false
      }
    },
    
    handleBack() {
      this.$router.back()
    },
    
    handleBreadcrumbClick(item) {
      if (item.to && item.to.name) {
        this.$router.push({ name: item.to.name })
      }
    }
  }
}
</script>

<style lang="less" scoped>
.industry-limit-config-container {
  min-height: 100vh;
  background: #fbf6ee;
}
</style>